const crypto = require('./extracted_crypto_function.js');
const axios = require('axios');
const { HttpsProxyAgent } = require('https-proxy-agent');
const zlib = require('zlib');
const fs = require('fs');

/**
 * 全局配置
 */
const baseUrl = "https://sdk.mini.stargame.group/api";
const token = "eyJhbGciOiJIUzI1NiJ9.eyJnYW1lSWQiOjEwLCJzaWduSW5UeXBlIjoiVVNFUiIsImdhbWVDaGFubmVsTWFzdGVyQ29kZU5vIjoiV0VDSEFUIiwic2Vzc2lvbktleSI6IlBBbTVwOW14Uzg0Y0N6VGRuVlBOVEE9PSIsImdhbWVDaGFubmVsTWFzdGVySWQiOjE2MSwiZ2FtZVVzZXJJZCI6MTk1NzY5MTE4NDY4NDA3NzA1NywiZ2FtZVVzZXJFeHRlcm5hbElkIjoibzZwaVg2Njh2eEVFQW1PRjZPM2FuNEszQ052ZyIsImdhbWVDaGFubmVsSWQiOjE3fQ.vUbIRP1bOWbzUtQgkYGhlB-fsTb9bWNvWgxhMkDY8cM";  // ⚠️注意替换

const headers = {
    "Authorization": `Bearer ${token}`,
    "Content-Type": "application/json",
    "Accept": "*/*",
    "xweb_xhr": "1",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090a13) UnifiedPCWindowsWechat(0xf2540517) XWEB/13909"
};

/**
 * 通用请求方法 - 使用 axios 和代理
 */
async function sendRequest(url, headers, payload = null) {
    try {
        // 创建代理配置
        const proxyAgent = new HttpsProxyAgent('http://127.0.0.1:2024');

        const config = {
            url: url,
            method: payload ? 'POST' : 'GET',
            headers: headers,
            httpsAgent: proxyAgent,
            httpAgent: proxyAgent,
            // 忽略SSL证书验证
            rejectUnauthorized: true,
            timeout: 30000, // 30秒超时
        };

        if (payload) {
            config.data = payload;
        }

        const response = await axios(config);
        return response.data;
    } catch (error) {
        // 如果是axios错误，提取响应数据
        if (error.response) {
            throw new Error(`请求失败: ${error.response.status} - ${JSON.stringify(error.response.data)}`);
        } else if (error.request) {
            throw new Error(`网络错误: ${error.message}`);
        } else {
            throw new Error(`请求配置错误: ${error.message}`);
        }
    }
}

/**
 * 获取存档
 */
async function getArchive(baseUrl, headers) {
    try {
        const url = baseUrl + "/game_user/get_game_archive"; // ⚠️确认路径
        const response = await sendRequest(url, headers);
        const res = typeof response === 'string' ? JSON.parse(response) : response;

        if (!res || !res.succeed) {
            throw new Error("获取存档失败: " + response);
        }

        const archiveHex = res.data.archive.replace(/\n/g, "");
        const binaryData = Buffer.from(archiveHex, 'hex');

        const decoded = zlib.gunzipSync(binaryData).toString();
        console.log("解压后的原始内容:", decoded);

        const data = JSON.parse(decoded);
        console.log("当前存档内容:");
        console.log(JSON.stringify(data, null, 2));

        return {
            data: data,
            version: parseInt(res.data.version)
        };
    } catch (error) {
        console.error("获取存档失败:", error.message);
        throw error;
    }
}

/**
 * 修改并上传存档
 */
async function updateArchive(baseUrl, headers, data, version) {
    try {
        const url = baseUrl + "/game_user/sync_game_archive";

        // 示例：改金币为 999999
        data['5'] = 999999;
        const key = rJsonData["12"].toString();  // 使用 用户uid 作为 密钥
        const result = crypto.signData(data, key);
        console.log("\nsignData 结果:", result);

        // 显示处理后的数据对象中的关键字段
        console.log("\n处理后的关键字段:");
        console.log("- 时间戳:", dataObject['timestamp']);
        console.log("- 签名:", dataObject[-1]);
        // 压缩 + 转 HEX
        const jsonStr = JSON.stringify(result);
        const gzData = zlib.gzipSync(jsonStr);
        const archiveHex = gzData.toString('hex');

        const payload = {
            "version": version, // 一般需要递增
            "archive": archiveHex
        };

        const response = await sendRequest(url, headers, payload);
        console.log("上传结果:");
        console.log(response);
    } catch (error) {
        console.error("上传存档失败:", error.message);
        throw error;
    }
}

/**
 * 主逻辑
 */
async function main() {
    try {
        console.log("开始获取存档...");
        const archiveInfo = await getArchive(baseUrl, headers);

        console.log("\n存档获取成功，版本:", archiveInfo.version);

        // 调试时可以先注释上传
        console.log("程序结束 (上传功能已注释)");
        return;

        // 取消注释下面这行来启用上传功能
        // await updateArchive(baseUrl, headers, archiveInfo.data, archiveInfo.version);

    } catch (error) {
        console.error("主程序执行失败:", error.message);
    }
}




// 如果需要测试存档功能，取消注释下面这行
main();